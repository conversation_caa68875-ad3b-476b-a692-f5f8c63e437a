import { render, screen, fireEvent } from '@testing-library/react';
import { WhatDoYouDoFacilityType } from '../Component';
import { WhatDoYouDoFacilityTypeProps, RoleCategoryType } from '../models';

// Mock dependencies
jest.mock('classnames', () => {
  return jest.fn((...args) => {
    return args
      .filter(Boolean)
      .map(arg => typeof arg === 'string' ? arg : Object.keys(arg).filter(key => arg[key]).join(' '))
      .join(' ');
  });
});

jest.mock('cx-dle-component-library', () => ({
  FormSection: jest.fn(({ className, children }) => (
    <div data-testid="form-section" className={className}>
      {children}
    </div>
  )),
  RichText: jest.fn(({ text, tag: Tag = 'div', className }) => (
    <Tag data-testid="rich-text" className={className}>
      {text}
    </Tag>
  )),
  Text: jest.fn(({ text, tag: Tag = 'div', className }) => (
    <Tag data-testid="text" className={className}>
      {text}
    </Tag>
  )),
}));

const mockRoleCategories: RoleCategoryType[] = [
  {
    name: 'customer',
    roleCategoryDescription: 'Healthcare facility or end user',
    roleCategoryName: 'Customer'
  },
  {
    name: 'dealer',
    roleCategoryDescription: 'Authorized dealer or distributor',
    roleCategoryName: 'Dealer'
  },
  {
    name: 'others',
    roleCategoryDescription: 'Other type of organization',
    roleCategoryName: 'Others'
  }
];

const baseProps: WhatDoYouDoFacilityTypeProps = {
  roleTitle: 'What type of facility do you work for?',
  roleCategories: mockRoleCategories,
  isFormValid: false,
  onFacilityTypeSelected: jest.fn(),
  facility: ''
};

describe('WhatDoYouDoFacilityType Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders without crashing', () => {
      render(<WhatDoYouDoFacilityType {...baseProps} />);
      expect(screen.getByTestId('form-section')).toBeInTheDocument();
    });

    it('renders with correct CSS class', () => {
      render(<WhatDoYouDoFacilityType {...baseProps} />);
      const formSection = screen.getByTestId('form-section');
      expect(formSection).toHaveClass('what-do-you-do-facility');
    });

    it('renders role title', () => {
      render(<WhatDoYouDoFacilityType {...baseProps} />);
      const richText = screen.getByTestId('rich-text');
      expect(richText).toHaveTextContent('What type of facility do you work for?');
    });

    it('renders facility container', () => {
      const { container } = render(<WhatDoYouDoFacilityType {...baseProps} />);
      expect(container.querySelector('.facility-container')).toBeInTheDocument();
    });
  });

  describe('Role Categories Rendering', () => {
    it('renders all role categories', () => {
      render(<WhatDoYouDoFacilityType {...baseProps} />);
      
      expect(screen.getByText('Customer')).toBeInTheDocument();
      expect(screen.getByText('Dealer')).toBeInTheDocument();
      expect(screen.getByText('Others')).toBeInTheDocument();
    });

    it('renders role category descriptions', () => {
      render(<WhatDoYouDoFacilityType {...baseProps} />);
      
      expect(screen.getByText('Healthcare facility or end user')).toBeInTheDocument();
      expect(screen.getByText('Authorized dealer or distributor')).toBeInTheDocument();
      expect(screen.getByText('Other type of organization')).toBeInTheDocument();
    });

    it('creates facility items with correct structure', () => {
      const { container } = render(<WhatDoYouDoFacilityType {...baseProps} />);
      
      const facilityItems = container.querySelectorAll('.facility-item');
      expect(facilityItems).toHaveLength(3);
      
      facilityItems.forEach(item => {
        expect(item.querySelector('.tile-title-description')).toBeInTheDocument();
        expect(item.querySelector('.tile-title')).toBeInTheDocument();
        expect(item.querySelector('.tile-description')).toBeInTheDocument();
      });
    });

    it('sets correct id for each facility item', () => {
      const { container } = render(<WhatDoYouDoFacilityType {...baseProps} />);
      
      expect(container.querySelector('#Customer')).toBeInTheDocument();
      expect(container.querySelector('#Dealer')).toBeInTheDocument();
      expect(container.querySelector('#Others')).toBeInTheDocument();
    });
  });

  describe('Selection State', () => {
    it('applies selected class when facility matches category name', () => {
      const props = { ...baseProps, facility: 'customer' };
      const { container } = render(<WhatDoYouDoFacilityType {...props} />);
      
      const customerItem = container.querySelector('#Customer');
      expect(customerItem).toHaveClass('facility-item selected');
    });

    it('does not apply selected class when facility does not match', () => {
      const props = { ...baseProps, facility: 'customer' };
      const { container } = render(<WhatDoYouDoFacilityType {...props} />);
      
      const dealerItem = container.querySelector('#Dealer');
      const othersItem = container.querySelector('#Others');
      
      expect(dealerItem).toHaveClass('facility-item');
      expect(dealerItem).not.toHaveClass('selected');
      expect(othersItem).toHaveClass('facility-item');
      expect(othersItem).not.toHaveClass('selected');
    });

    it('shows correct check/uncheck circle based on selection', () => {
      const props = { ...baseProps, facility: 'customer' };
      const { container } = render(<WhatDoYouDoFacilityType {...props} />);
      
      const customerItem = container.querySelector('#Customer');
      const dealerItem = container.querySelector('#Dealer');
      
      expect(customerItem?.querySelector('.tile-check-circle')).toBeInTheDocument();
      expect(dealerItem?.querySelector('.tile-uncheck-circle')).toBeInTheDocument();
    });
  });

  describe('Error State', () => {
    it('applies error class when isFormValid is true and no facility selected', () => {
      const props = { ...baseProps, isFormValid: true, facility: '' };
      render(<WhatDoYouDoFacilityType {...props} />);
      
      const richText = screen.getByTestId('rich-text');
      expect(richText).toHaveClass('describe-your-job-label error');
    });

    it('applies error class to facility items when isFormValid is true and no facility selected', () => {
      const props = { ...baseProps, isFormValid: true, facility: '' };
      const { container } = render(<WhatDoYouDoFacilityType {...props} />);
      
      const facilityItems = container.querySelectorAll('.facility-item');
      facilityItems.forEach(item => {
        expect(item).toHaveClass('error');
      });
    });

    it('does not apply error class when facility is selected', () => {
      const props = { ...baseProps, isFormValid: true, facility: 'customer' };
      render(<WhatDoYouDoFacilityType {...props} />);
      
      const richText = screen.getByTestId('rich-text');
      expect(richText).toHaveClass('describe-your-job-label');
      expect(richText).not.toHaveClass('error');
    });

    it('does not apply error class when isFormValid is false', () => {
      const props = { ...baseProps, isFormValid: false, facility: '' };
      render(<WhatDoYouDoFacilityType {...props} />);
      
      const richText = screen.getByTestId('rich-text');
      expect(richText).toHaveClass('describe-your-job-label');
      expect(richText).not.toHaveClass('error');
    });
  });

  describe('Click Handling', () => {
    it('calls onFacilityTypeSelected when facility item is clicked', () => {
      const mockOnFacilityTypeSelected = jest.fn();
      const props = { ...baseProps, onFacilityTypeSelected: mockOnFacilityTypeSelected };
      
      const { container } = render(<WhatDoYouDoFacilityType {...props} />);
      
      const customerItem = container.querySelector('#Customer');
      fireEvent.click(customerItem!);
      
      expect(mockOnFacilityTypeSelected).toHaveBeenCalledTimes(1);
      expect(mockOnFacilityTypeSelected).toHaveBeenCalledWith('customer');
    });

    it('calls onFacilityTypeSelected with correct category name for different items', () => {
      const mockOnFacilityTypeSelected = jest.fn();
      const props = { ...baseProps, onFacilityTypeSelected: mockOnFacilityTypeSelected };
      
      const { container } = render(<WhatDoYouDoFacilityType {...props} />);
      
      const dealerItem = container.querySelector('#Dealer');
      const othersItem = container.querySelector('#Others');
      
      fireEvent.click(dealerItem!);
      expect(mockOnFacilityTypeSelected).toHaveBeenCalledWith('dealer');
      
      fireEvent.click(othersItem!);
      expect(mockOnFacilityTypeSelected).toHaveBeenCalledWith('others');
    });

    it('handles multiple clicks on the same item', () => {
      const mockOnFacilityTypeSelected = jest.fn();
      const props = { ...baseProps, onFacilityTypeSelected: mockOnFacilityTypeSelected };
      
      const { container } = render(<WhatDoYouDoFacilityType {...props} />);
      
      const customerItem = container.querySelector('#Customer');
      fireEvent.click(customerItem!);
      fireEvent.click(customerItem!);
      
      expect(mockOnFacilityTypeSelected).toHaveBeenCalledTimes(2);
      expect(mockOnFacilityTypeSelected).toHaveBeenNthCalledWith(1, 'customer');
      expect(mockOnFacilityTypeSelected).toHaveBeenNthCalledWith(2, 'customer');
    });
  });

  describe('Props Handling', () => {
    it('handles different roleTitle values', () => {
      const props = { ...baseProps, roleTitle: 'Custom role title' };
      render(<WhatDoYouDoFacilityType {...props} />);
      
      const richText = screen.getByTestId('rich-text');
      expect(richText).toHaveTextContent('Custom role title');
    });

    it('handles empty roleCategories array', () => {
      const props = { ...baseProps, roleCategories: [] };
      const { container } = render(<WhatDoYouDoFacilityType {...props} />);
      
      const facilityItems = container.querySelectorAll('.facility-item');
      expect(facilityItems).toHaveLength(0);
    });

    it('handles single role category', () => {
      const singleCategory: RoleCategoryType[] = [{
        name: 'single',
        roleCategoryDescription: 'Single category',
        roleCategoryName: 'Single'
      }];
      
      const props = { ...baseProps, roleCategories: singleCategory };
      const { container } = render(<WhatDoYouDoFacilityType {...props} />);
      
      const facilityItems = container.querySelectorAll('.facility-item');
      expect(facilityItems).toHaveLength(1);
      expect(screen.getByText('Single')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles null roleCategories', () => {
      const props = { ...baseProps, roleCategories: null as any };
      const { container } = render(<WhatDoYouDoFacilityType {...props} />);
      
      const facilityItems = container.querySelectorAll('.facility-item');
      expect(facilityItems).toHaveLength(0);
    });

    it('handles undefined roleCategories', () => {
      const props = { ...baseProps, roleCategories: undefined as any };
      const { container } = render(<WhatDoYouDoFacilityType {...props} />);
      
      const facilityItems = container.querySelectorAll('.facility-item');
      expect(facilityItems).toHaveLength(0);
    });

    it('handles empty string facility value', () => {
      const props = { ...baseProps, facility: '' };
      const { container } = render(<WhatDoYouDoFacilityType {...props} />);
      
      const facilityItems = container.querySelectorAll('.facility-item');
      facilityItems.forEach(item => {
        expect(item).not.toHaveClass('selected');
        expect(item.querySelector('.tile-uncheck-circle')).toBeInTheDocument();
      });
    });

    it('handles facility value that does not match any category', () => {
      const props = { ...baseProps, facility: 'nonexistent' };
      const { container } = render(<WhatDoYouDoFacilityType {...props} />);
      
      const facilityItems = container.querySelectorAll('.facility-item');
      facilityItems.forEach(item => {
        expect(item).not.toHaveClass('selected');
        expect(item.querySelector('.tile-uncheck-circle')).toBeInTheDocument();
      });
    });
  });

  describe('Accessibility', () => {
    it('provides clickable elements for keyboard navigation', () => {
      const { container } = render(<WhatDoYouDoFacilityType {...baseProps} />);
      
      const facilityItems = container.querySelectorAll('.facility-item');
      facilityItems.forEach(item => {
        expect(item).toHaveAttribute('id');
      });
    });

    it('maintains proper semantic structure', () => {
      const { container } = render(<WhatDoYouDoFacilityType {...baseProps} />);
      
      const formSection = container.querySelector('[data-testid="form-section"]');
      const facilityContainer = container.querySelector('.facility-container');
      
      expect(formSection).toBeInTheDocument();
      expect(facilityContainer).toBeInTheDocument();
    });
  });
});
