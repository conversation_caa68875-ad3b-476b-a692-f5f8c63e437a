import { render, screen } from '@testing-library/react';
import { SelectedPreferences } from '../Component';
import { SelectedPreferencesProps } from '../models';

// Mock cx-dle-component-library
jest.mock('cx-dle-component-library', () => ({
  IconBadge: jest.fn(({ children, className }) => (
    <div data-testid="icon-badge" className={className}>
      {children}
    </div>
  )),
}));

const baseProps: SelectedPreferencesProps = {
  preferences: ['Preference 1', 'Preference 2', 'Preference 3'],
  topFollowingCount: 5,
  isDepartment: false,
  totalDepartmentPreferredCount: 2,
  preferencesThreshold: 3
};

describe('SelectedPreferences Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders without crashing', () => {
      render(<SelectedPreferences {...baseProps} />);
      expect(screen.getByText('Preference 1, Preference 2, Preference 3')).toBeInTheDocument();
    });

    it('renders with correct CSS class', () => {
      const { container } = render(<SelectedPreferences {...baseProps} />);
      expect(container.querySelector('.ge-selected-preferences')).toBeInTheDocument();
    });

    it('renders preferences names with correct class', () => {
      const { container } = render(<SelectedPreferences {...baseProps} />);
      const namesElement = container.querySelector('.ge-selected-preferences__names');
      expect(namesElement).toBeInTheDocument();
      expect(namesElement).toHaveTextContent('Preference 1, Preference 2, Preference 3');
    });

    it('does not render when preferences is null', () => {
      render(<SelectedPreferences {...baseProps} preferences={null as any} />);
      expect(screen.queryByText('Preference 1')).not.toBeInTheDocument();
    });

    it('does not render when preferences is undefined', () => {
      render(<SelectedPreferences {...baseProps} preferences={undefined as any} />);
      expect(screen.queryByText('Preference 1')).not.toBeInTheDocument();
    });
  });

  describe('Preferences Threshold', () => {
    it('displays only preferences up to threshold', () => {
      const props = {
        ...baseProps,
        preferences: ['Pref 1', 'Pref 2', 'Pref 3', 'Pref 4', 'Pref 5'],
        preferencesThreshold: 2
      };
      render(<SelectedPreferences {...props} />);
      expect(screen.getByText('Pref 1, Pref 2')).toBeInTheDocument();
      expect(screen.queryByText('Pref 3')).not.toBeInTheDocument();
    });

    it('uses default threshold of 3 when not provided', () => {
      const props = {
        preferences: ['Pref 1', 'Pref 2', 'Pref 3', 'Pref 4'],
        isDepartment: false
      };
      render(<SelectedPreferences {...props} />);
      expect(screen.getByText('Pref 1, Pref 2, Pref 3')).toBeInTheDocument();
    });

    it('displays all preferences when count is less than threshold', () => {
      const props = {
        ...baseProps,
        preferences: ['Pref 1', 'Pref 2'],
        preferencesThreshold: 5
      };
      render(<SelectedPreferences {...props} />);
      expect(screen.getByText('Pref 1, Pref 2')).toBeInTheDocument();
    });
  });

  describe('Null Filtering', () => {
    it('filters out null values from preferences', () => {
      const props = {
        ...baseProps,
        preferences: ['Pref 1', null, 'Pref 2', null, 'Pref 3'] as any
      };
      render(<SelectedPreferences {...props} />);
      expect(screen.getByText('Pref 1, Pref 2, Pref 3')).toBeInTheDocument();
    });

    it('handles array with only null values', () => {
      const props = {
        ...baseProps,
        preferences: [null, null, null] as any
      };
      const { container } = render(<SelectedPreferences {...props} />);
      const namesElement = container.querySelector('.ge-selected-preferences__names');
      expect(namesElement).toBeInTheDocument();
      expect(namesElement).toHaveTextContent('');
    });
  });

  describe('Non-Department Mode Badge', () => {
    it('shows badge when preferences exceed threshold', () => {
      const props = {
        ...baseProps,
        preferences: ['Pref 1', 'Pref 2', 'Pref 3', 'Pref 4', 'Pref 5'],
        isDepartment: false,
        preferencesThreshold: 3
      };
      render(<SelectedPreferences {...props} />);
      
      const badge = screen.getByTestId('icon-badge');
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveTextContent('+2');
      expect(badge).toHaveClass('ge-selected-preferences__badge');
    });

    it('does not show badge when preferences equal threshold', () => {
      const props = {
        ...baseProps,
        preferences: ['Pref 1', 'Pref 2', 'Pref 3'],
        isDepartment: false,
        preferencesThreshold: 3
      };
      render(<SelectedPreferences {...props} />);
      expect(screen.queryByTestId('icon-badge')).not.toBeInTheDocument();
    });

    it('does not show badge when preferences are less than threshold', () => {
      const props = {
        ...baseProps,
        preferences: ['Pref 1', 'Pref 2'],
        isDepartment: false,
        preferencesThreshold: 3
      };
      render(<SelectedPreferences {...props} />);
      expect(screen.queryByTestId('icon-badge')).not.toBeInTheDocument();
    });
  });

  describe('Department Mode Badge', () => {
    it('shows badge when conditions are met for department mode', () => {
      const props = {
        ...baseProps,
        preferences: ['Pref 1', 'Pref 2', 'Pref 3'],
        isDepartment: true,
        topFollowingCount: 5,
        totalDepartmentPreferredCount: 2,
        preferencesThreshold: 3
      };
      render(<SelectedPreferences {...props} />);
      
      const badge = screen.getByTestId('icon-badge');
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveTextContent('+3'); // Math.abs(5 - 2) = 3
    });

    it('does not show badge when topFollowingCount is less than threshold', () => {
      const props = {
        ...baseProps,
        preferences: ['Pref 1', 'Pref 2', 'Pref 3'],
        isDepartment: true,
        topFollowingCount: 2,
        totalDepartmentPreferredCount: 1,
        preferencesThreshold: 3
      };
      render(<SelectedPreferences {...props} />);
      expect(screen.queryByTestId('icon-badge')).not.toBeInTheDocument();
    });

    it('does not show badge when preferredCount is 0', () => {
      const props = {
        ...baseProps,
        preferences: ['Pref 1', 'Pref 2', 'Pref 3'],
        isDepartment: true,
        topFollowingCount: 5,
        totalDepartmentPreferredCount: 5,
        preferencesThreshold: 3
      };
      render(<SelectedPreferences {...props} />);
      expect(screen.queryByTestId('icon-badge')).not.toBeInTheDocument();
    });

    it('does not show badge when topFollowingCount is undefined', () => {
      const props = {
        ...baseProps,
        preferences: ['Pref 1', 'Pref 2', 'Pref 3'],
        isDepartment: true,
        topFollowingCount: undefined,
        totalDepartmentPreferredCount: 2,
        preferencesThreshold: 3
      };
      render(<SelectedPreferences {...props} />);
      expect(screen.queryByTestId('icon-badge')).not.toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles empty preferences array', () => {
      const props = {
        ...baseProps,
        preferences: []
      };
      const { container } = render(<SelectedPreferences {...props} />);
      const namesElement = container.querySelector('.ge-selected-preferences__names');
      expect(namesElement).toBeInTheDocument();
      expect(namesElement).toHaveTextContent('');
    });

    it('handles undefined totalDepartmentPreferredCount in department mode', () => {
      const props = {
        ...baseProps,
        preferences: ['Pref 1', 'Pref 2', 'Pref 3'],
        isDepartment: true,
        topFollowingCount: 5,
        totalDepartmentPreferredCount: undefined,
        preferencesThreshold: 3
      };
      render(<SelectedPreferences {...props} />);

      // When totalDepartmentPreferredCount is undefined, (5 - undefined) results in NaN
      // Math.abs(NaN) || 0 = 0, so no badge should be shown
      expect(screen.queryByTestId('icon-badge')).not.toBeInTheDocument();
    });

    it('handles zero preferencesThreshold', () => {
      const props = {
        ...baseProps,
        preferences: ['Pref 1', 'Pref 2'],
        preferencesThreshold: 0,
        isDepartment: false
      };
      const { container } = render(<SelectedPreferences {...props} />);
      const namesElement = container.querySelector('.ge-selected-preferences__names');
      expect(namesElement).toHaveTextContent('');

      const badge = screen.getByTestId('icon-badge');
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveTextContent('+2');
    });
  });

  describe('Math.abs Calculation', () => {
    it('correctly calculates absolute difference for department mode', () => {
      const props = {
        ...baseProps,
        preferences: ['Pref 1', 'Pref 2', 'Pref 3'],
        isDepartment: true,
        topFollowingCount: 2,
        totalDepartmentPreferredCount: 5,
        preferencesThreshold: 3
      };
      render(<SelectedPreferences {...props} />);
      expect(screen.queryByTestId('icon-badge')).not.toBeInTheDocument(); // topFollowingCount < threshold
    });

    it('handles negative difference correctly', () => {
      const props = {
        ...baseProps,
        preferences: ['Pref 1', 'Pref 2', 'Pref 3'],
        isDepartment: true,
        topFollowingCount: 5,
        totalDepartmentPreferredCount: 8,
        preferencesThreshold: 3
      };
      render(<SelectedPreferences {...props} />);
      
      const badge = screen.getByTestId('icon-badge');
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveTextContent('+3'); // Math.abs(5 - 8) = 3
    });
  });
});
