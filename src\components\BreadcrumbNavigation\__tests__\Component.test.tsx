import { render, screen } from '@testing-library/react';
import { BreadcrumbNavigation } from '../Component';
import { BreadcrumbProps } from '../model';

// Mock dependencies
jest.mock('cx-dle-component-library', () => ({
  Breadcrumb: jest.fn(({ 
    isNoVerticalPadding, 
    isNoHorizontalPadding, 
    productName, 
    breadcrumbBackIcon, 
    separator, 
    backRedirectUrl, 
    backRedirectText, 
    isShadow, 
    analyticsEventName, 
    analyticsLinkName, 
    analyticsLinkType, 
    analyticsLinkTitle 
  }) => (
    <div data-testid="breadcrumb">
      <div data-testid="product-name">{productName}</div>
      <div data-testid="back-icon">{breadcrumbBackIcon}</div>
      <div data-testid="separator">{separator}</div>
      <div data-testid="back-url">{backRedirectUrl}</div>
      <div data-testid="back-text">{backRedirectText}</div>
      <div data-testid="no-vertical-padding">{isNoVerticalPadding?.toString()}</div>
      <div data-testid="no-horizontal-padding">{isNoHorizontalPadding?.toString()}</div>
      <div data-testid="is-shadow">{isShadow?.toString()}</div>
      <div data-testid="analytics-event-name">{analyticsEventName}</div>
      <div data-testid="analytics-link-name">{analyticsLinkName}</div>
      <div data-testid="analytics-link-type">{analyticsLinkType}</div>
      <div data-testid="analytics-link-title">{analyticsLinkTitle}</div>
    </div>
  )),
}));

jest.mock('cx-dle-common-lib', () => ({
  OMNITURE_EVENTS: {
    LINK_CLICK: 'link-click'
  }
}));

const baseProps: BreadcrumbProps = {
  productName: 'Account Management',
  backIcon: 'ico-arrow-left-16',
  separator: '>',
  backRedirectUrl: '/dashboard',
  backRedirectText: 'Back to Dashboard'
};

describe('BreadcrumbNavigation Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders without crashing', () => {
      render(<BreadcrumbNavigation {...baseProps} />);
      expect(screen.getByTestId('breadcrumb')).toBeInTheDocument();
    });

    it('renders with correct product name', () => {
      render(<BreadcrumbNavigation {...baseProps} />);
      expect(screen.getByTestId('product-name')).toHaveTextContent('Account Management');
    });

    it('renders with correct back icon', () => {
      render(<BreadcrumbNavigation {...baseProps} />);
      expect(screen.getByTestId('back-icon')).toHaveTextContent('ico-arrow-left-16');
    });

    it('renders with correct separator', () => {
      render(<BreadcrumbNavigation {...baseProps} />);
      expect(screen.getByTestId('separator')).toHaveTextContent('>');
    });

    it('renders with correct back redirect URL', () => {
      render(<BreadcrumbNavigation {...baseProps} />);
      expect(screen.getByTestId('back-url')).toHaveTextContent('/dashboard');
    });

    it('renders with correct back redirect text', () => {
      render(<BreadcrumbNavigation {...baseProps} />);
      expect(screen.getByTestId('back-text')).toHaveTextContent('Back to Dashboard');
    });
  });

  describe('Fixed Props Configuration', () => {
    it('sets isNoVerticalPadding to true', () => {
      render(<BreadcrumbNavigation {...baseProps} />);
      expect(screen.getByTestId('no-vertical-padding')).toHaveTextContent('true');
    });

    it('sets isNoHorizontalPadding to false', () => {
      render(<BreadcrumbNavigation {...baseProps} />);
      expect(screen.getByTestId('no-horizontal-padding')).toHaveTextContent('false');
    });

    it('sets isShadow to true', () => {
      render(<BreadcrumbNavigation {...baseProps} />);
      expect(screen.getByTestId('is-shadow')).toHaveTextContent('true');
    });
  });

  describe('Analytics Configuration', () => {
    it('sets correct analytics event name', () => {
      render(<BreadcrumbNavigation {...baseProps} />);
      expect(screen.getByTestId('analytics-event-name')).toHaveTextContent('link-click');
    });

    it('sets analytics link name to backRedirectText', () => {
      render(<BreadcrumbNavigation {...baseProps} />);
      expect(screen.getByTestId('analytics-link-name')).toHaveTextContent('Back to Dashboard');
    });

    it('sets analytics link type to "header breadcrumb"', () => {
      render(<BreadcrumbNavigation {...baseProps} />);
      expect(screen.getByTestId('analytics-link-type')).toHaveTextContent('header breadcrumb');
    });

    it('sets analytics link title to productName', () => {
      render(<BreadcrumbNavigation {...baseProps} />);
      expect(screen.getByTestId('analytics-link-title')).toHaveTextContent('Account Management');
    });
  });

  describe('Props Handling', () => {
    it('handles different productName values', () => {
      const props = { ...baseProps, productName: 'User Settings' };
      render(<BreadcrumbNavigation {...props} />);
      
      expect(screen.getByTestId('product-name')).toHaveTextContent('User Settings');
      expect(screen.getByTestId('analytics-link-title')).toHaveTextContent('User Settings');
    });

    it('handles different backIcon values', () => {
      const props = { ...baseProps, backIcon: 'ico-chevron-left-16' };
      render(<BreadcrumbNavigation {...props} />);
      
      expect(screen.getByTestId('back-icon')).toHaveTextContent('ico-chevron-left-16');
    });

    it('handles different separator values', () => {
      const props = { ...baseProps, separator: '/' };
      render(<BreadcrumbNavigation {...props} />);
      
      expect(screen.getByTestId('separator')).toHaveTextContent('/');
    });

    it('handles different backRedirectUrl values', () => {
      const props = { ...baseProps, backRedirectUrl: '/home' };
      render(<BreadcrumbNavigation {...props} />);
      
      expect(screen.getByTestId('back-url')).toHaveTextContent('/home');
    });

    it('handles different backRedirectText values', () => {
      const props = { ...baseProps, backRedirectText: 'Go Home' };
      render(<BreadcrumbNavigation {...props} />);
      
      expect(screen.getByTestId('back-text')).toHaveTextContent('Go Home');
      expect(screen.getByTestId('analytics-link-name')).toHaveTextContent('Go Home');
    });
  });

  describe('Edge Cases', () => {
    it('handles empty string values', () => {
      const props: BreadcrumbProps = {
        productName: '',
        backIcon: '',
        separator: '',
        backRedirectUrl: '',
        backRedirectText: ''
      };
      
      render(<BreadcrumbNavigation {...props} />);
      
      expect(screen.getByTestId('product-name')).toHaveTextContent('');
      expect(screen.getByTestId('back-icon')).toHaveTextContent('');
      expect(screen.getByTestId('separator')).toHaveTextContent('');
      expect(screen.getByTestId('back-url')).toHaveTextContent('');
      expect(screen.getByTestId('back-text')).toHaveTextContent('');
    });

    it('handles special characters in props', () => {
      const props: BreadcrumbProps = {
        productName: 'Account & Settings',
        backIcon: 'ico-arrow-left-16',
        separator: '>>',
        backRedirectUrl: '/dashboard?tab=settings&view=all',
        backRedirectText: 'Back to Dashboard & Settings'
      };
      
      render(<BreadcrumbNavigation {...props} />);
      
      expect(screen.getByTestId('product-name')).toHaveTextContent('Account & Settings');
      expect(screen.getByTestId('separator')).toHaveTextContent('>>'); // Remove extra spaces
      expect(screen.getByTestId('back-url')).toHaveTextContent('/dashboard?tab=settings&view=all');
      expect(screen.getByTestId('back-text')).toHaveTextContent('Back to Dashboard & Settings');
    });

    it('handles long text values', () => {
      const longProductName = 'Very Long Product Name That Might Cause Layout Issues';
      const longBackText = 'Very Long Back Text That Might Also Cause Layout Issues';
      
      const props: BreadcrumbProps = {
        productName: longProductName,
        backIcon: 'ico-arrow-left-16',
        separator: '>',
        backRedirectUrl: '/dashboard',
        backRedirectText: longBackText
      };
      
      render(<BreadcrumbNavigation {...props} />);
      
      expect(screen.getByTestId('product-name')).toHaveTextContent(longProductName);
      expect(screen.getByTestId('back-text')).toHaveTextContent(longBackText);
    });
  });

  describe('Component Integration', () => {
    it('passes all required props to Breadcrumb component', () => {
      render(<BreadcrumbNavigation {...baseProps} />);
      
      // Verify all props are passed correctly
      expect(screen.getByTestId('product-name')).toBeInTheDocument();
      expect(screen.getByTestId('back-icon')).toBeInTheDocument();
      expect(screen.getByTestId('separator')).toBeInTheDocument();
      expect(screen.getByTestId('back-url')).toBeInTheDocument();
      expect(screen.getByTestId('back-text')).toBeInTheDocument();
      expect(screen.getByTestId('no-vertical-padding')).toBeInTheDocument();
      expect(screen.getByTestId('no-horizontal-padding')).toBeInTheDocument();
      expect(screen.getByTestId('is-shadow')).toBeInTheDocument();
      expect(screen.getByTestId('analytics-event-name')).toBeInTheDocument();
      expect(screen.getByTestId('analytics-link-name')).toBeInTheDocument();
      expect(screen.getByTestId('analytics-link-type')).toBeInTheDocument();
      expect(screen.getByTestId('analytics-link-title')).toBeInTheDocument();
    });

    it('maintains consistent analytics configuration', () => {
      const props1 = { ...baseProps, productName: 'Product A', backRedirectText: 'Back A' };
      const { rerender } = render(<BreadcrumbNavigation {...props1} />);
      
      expect(screen.getByTestId('analytics-link-title')).toHaveTextContent('Product A');
      expect(screen.getByTestId('analytics-link-name')).toHaveTextContent('Back A');
      
      const props2 = { ...baseProps, productName: 'Product B', backRedirectText: 'Back B' };
      rerender(<BreadcrumbNavigation {...props2} />);
      
      expect(screen.getByTestId('analytics-link-title')).toHaveTextContent('Product B');
      expect(screen.getByTestId('analytics-link-name')).toHaveTextContent('Back B');
    });
  });

  describe('TypeScript Props Interface', () => {
    it('accepts all required props from BreadcrumbProps interface', () => {
      const validProps: BreadcrumbProps = {
        productName: 'Test Product',
        backIcon: 'test-icon',
        separator: '|',
        backRedirectUrl: '/test',
        backRedirectText: 'Test Back'
      };
      
      render(<BreadcrumbNavigation {...validProps} />);
      expect(screen.getByTestId('breadcrumb')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('provides proper structure for screen readers', () => {
      render(<BreadcrumbNavigation {...baseProps} />);
      
      // The component should render the Breadcrumb component which handles accessibility
      expect(screen.getByTestId('breadcrumb')).toBeInTheDocument();
      expect(screen.getByTestId('back-text')).toHaveTextContent('Back to Dashboard');
      expect(screen.getByTestId('product-name')).toHaveTextContent('Account Management');
    });
  });
});
